trigger: none

appendCommitMessageToRunName: false
name: $(date:yyyyMMdd)$(rev:.r) • ${{ parameters.environment }} • ${{ coalesce(split(parameters.example, '/')[2], split(parameters.example, '/')[1]) }} • terratest

parameters:
  - name: environment
    type: string
    default: TST-iac

  - name: example
    type: string
    default: nosql_database/examples/04-nosql_database-default

  - name: test
    type: string
    default: terratest/04-nosql_database-default

  - name: no_proxy
    type: string
    default: "*.core.windows.net"

variables:
  - group: 'Centrally managed variable group'
  - template: env/${{ parameters.environment }}.yaml@tooling

resources:
  repositories:
    - repository: tooling
      type: git
      name: tooling
      ref: refs/tags/v2

    - repository: pipelinetemplates
      type: git
      name: OTPHU-CDO-ADOS-TOOLS/pipelinetemplates
      ref: refs/tags/v6

extends:
  template: iac-pipelines/iac-terratest.yaml@tooling
  parameters:
    environment: ${{ parameters.environment }}
    appcode: ${{ variables.appCode }}
    destroy: true
    destroyResourceGroupName: rgrp-weu-tst-sqld0t2
    no_proxy: ${{ parameters.no_proxy }}
    timeout_in_minutes: 90
    armServiceConnectionName: ${{ variables.armServiceConnectionName }}
    storageAccountResourceGroup: ${{ variables.storageAccountResourceGroup }}
    storageAccountName: ${{ variables.storageAccountName }}
    storageAccountContainerName: ${{ variables.storageAccountContainerName }}
    keyVaultServiceConnectionName: ${{ variables.keyVaultServiceConnectionName }}
    KeyVaultName: ${{ variables.keyVaultName }}
    keyVaultCommaSeparatedSecretNames: ${{ variables.keyVaultCommaSeparatedSecretNames }}
    terraformProjectLocation: ${{ parameters.example}}
    terraformVersion: '1.7.4'
    terraformRCFileForNetworkMirror: "network-mirror/.terraformrc"
    goTerratestLocation: ${{ parameters.test}}
