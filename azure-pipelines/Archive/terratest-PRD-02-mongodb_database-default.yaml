trigger:
- none

parameters:

  - name: no_proxy
    type: string
    default: "*.core.windows.net"

resources:
  repositories:
    - repository: tooling
      type: git
      name: tooling
      ref: refs/tags/v1

stages:

  - stage: Terratest
    jobs:
      - template: pipelines/iac-terratest.yaml@tooling
        parameters:
          no_proxy: ${{ parameters.no_proxy }}
          target: OTP-ADO-IaC-sub-prd-01
          test: 02-mongodb_database-default
          test_dir: terratest/02-mongodb_database-default
          iac_dir: mongodb_database/examples/02-mongodb_database-default
          custom_agent_pool_name: PRD-AksPool-centralagent-Deploy
          timeout_in_minutes: 90
