trigger:
- none

parameters:

  - name: no_proxy
    type: string
    default: "*.core.windows.net"

resources:
  repositories:
    - repository: tooling
      type: git
      name: tooling
      ref: refs/tags/v1

stages:

  - stage: Terratest
    jobs:
      - template: pipelines/iac-terratest.yaml@tooling
        parameters:
          no_proxy: ${{ parameters.no_proxy }}
          target: OTP-ADO-IaC-sub-tst-01
          test: 01-mongodb-default
          test_dir: terratest/01-mongodb-default
          iac_dir: mongodb/examples/01-mongodb-default
          custom_agent_pool_name: TST-AksPool-centralagent-Deploy
          timeout_in_minutes: 90