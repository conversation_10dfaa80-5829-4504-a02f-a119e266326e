variable "conventions" {
  description = "(Required) terraform-conventions module"

  validation {
    condition     = can(regex("^[-A-Za-z0-9]{2,}$", var.conventions.project))
    error_message = "Only alphanumeric characters and hyphens are allowed and should contain at least two characters."
  }
}

variable "resource_name_suffix" {
  description = "(Required) Custom resource name suffix"
  type        = string
  validation {
    condition     = can(regex("^[-A-Za-z0-9]{2,}$", var.resource_name_suffix))
    error_message = "Only alphanumeric characters and hyphens are allowed and should contain at least two characters"
  }

}

variable "resource_group_name" {
  description ="(Required) Specifies the name of the resource group in which to create the Cache for Redis."
  type        = string
}

variable "subnet_id" {
  description = "(Required) Subnet where private endpoint will be created"
  type        = string
}

// CosmosDB specific variables

variable "failover_locations" {
  description = "(Optional) The name of the Azure region to host replicated data and their priority. One location is always required."
  type = list(object({
    location          = string
    failover_priority = number
    zone_redundant    = optional(bool)
  }))
  default = null
}

variable "consistency_policy" {
  description = "(Required) Consistency levels in Azure Cosmos DB"
  type = object({
    consistency_level       = string
    max_interval_in_seconds = optional(number)
    max_staleness_prefix    = optional(number)
  })
}

variable "capabilities" {
  description = "(Optional) The capabilities which should be enabled for this Cosmos DB account."
  type = list(string)
  default = []
}

variable "total_throughput_limit" {
  description = "(Optional) The total throughput limit imposed on this Cosmos DB account (RU/s). Default is nolimit: -1"
  type        = number
  default     = -1
}

variable "backup" {
  type = map(string)
  default = {
    type                = "Periodic"
    interval_in_minutes = 240
    retention_in_hours  = null
    storage_redundancy  = "Local"
  }
  description = <<-EOT
  Specifies the backup setting for different types, intervals and retention time in hours that each backup is retained.  Default retention is 7 days on dev, tst and 30 days on ppr, prd.
    type - (Required) The type of the backup. Possible values are Continuous and Periodic. Migration of Periodic to Continuous is one-way, changing Continuous to Periodic forces a new resource to be created.
    interval_in_minutes - (Optional) The interval in minutes between two backups. This is configurable only when type is Periodic. Possible values are between 60 and 1440.
    retention_in_hours - (Optional) The time in hours that each backup is retained. This is configurable only when type is Periodic. Possible values are between 8 and 720.
    storage_redundancy - (Optional) The storage redundancy is used to indicate the type of backup residency. This is configurable only when type is Periodic. Possible values are Geo, Local and Zone.
  EOT
}

variable "ip_range_filter" {
  type        = list(string)
  default     = [""]
  description = "(Optional) This value specifies the set of IP addresses or IP address ranges in CIDR form to be included as the allowed list of client IPs for a given database account. IP addresses/ranges must be comma separated and must not contain any spaces."
}


variable "minimal_tls_version" {
  type        = string
  default     = "Tls12"
  description = "(Optional) Specifies the minimal TLS version for the CosmosDB account. Possible values are: Tls, Tls11, and Tls12. Defaults to Tls12."
}

//Private endpoint setting
variable "wait_after" {
  type        = number
  default     = 600
  description = "(Optional) Seconds to wait after private endpoint is created."
}

variable "pe_subresource_list" {
  type        = list(string)
  default     = ["SQL"]
  description = "A list of subresource names which the Private Endpoint is able to connect to."
}

//Diagnostic settings variables
variable "log_analytics_workspace_id" {
  description = "(Optional) ID of target Log Analytics Workspace"
  type        = string
  default     = null
}

variable "log_analytics_diag_logs" {
  description = "(Optional) List log types need to be sent to Log Analytics Workspace. Set AllLogs to send all available log types. Check available log types: https://learn.microsoft.com/en-us/azure/azure-monitor/essentials/resource-logs-categories"
  type        = list(string)
  default     = []
}

variable "log_analytics_metrics" {
  description = "(Optional) List metrics need to be sent to Log Analytics Workspace. Set AllMetrics to send all available metric types."
  type        = list(string)
  default     = []
}


//Monitoring and alerts
variable "resource_health_monitoring" {
  description = "(Optional) Set to false if resource health alert rule is not required. Defaults to true."
  type        = bool
  default     = true
}

variable "resource_health_alert_location" {
  type        = string
  description = "(Optional) Region where the alert rule will be created. Defaults to West Europe, North Europe or global according to conventions settings."
  default     = null  
}


variable "builtin_metric_monitoring" {
  description = "(Optional) Set to false if default alerting rules are not required. Defaults to true"
  type        = bool
  default     = true
}

variable "alert_DedicatedGatewayAverageCPUUsage_threshold" {
  description = "(Optional) Threshold for Dedicated Gateway Average CPU Usage alert rule."
  type        = number
  default     = 90
}

variable "alert_NormalizedRUConsumption_threshold" {
  description = "(Optional) Threshold for Normalized RU Consumption alert rule."
  type        = number
  default     = 90
}

variable "alert_ServiceAvailability_threshold" {
  description = "(Optional) Threshold for Service Availability alert rule."
  type        = number
  default     = 99
}

variable "nosql_tags" {
  description = "(Optional) Additional tags to apply to the NoSQL account and additional resources created by the module"
  type        = map(string)
  default     = null
}

// RBAC

variable "operator_ids" {
  type        = list(string)
  default     = []
  description = "(Optional) The Principal IDs of the  object (User, Group or Service Principal) to assign the Cosmos DB Operator role. This role is always assigned to the running service principal."
}
