## Name of the module
Azure Cosmos DB NoSQL Account

shortname: cnsq

terraform resource: azurerm_cosmosdb_account

## Short description of the module
This Terraform module deploys an Azure Cosmos DB NoSQL Account.

## Detailed description on Confluence
[Azure Cosmos DB ](https://confluence.otpbank.hu/x/7YN6Kw) 

## Release notes – changes in the current and previous versions
[CHANGELOG.md](../CHANGELOG.md)

## Terraform version compatibility
Terraform >= 1.7.4

## Necessary Terraform providers, and compatibility to provider versions
provider registry.terraform.io/hashicorp/azurerm >= 4.2.0
provider registry.terraform.io/hashicorp/time >= 0.9.1

## Resources generated by the module
- Azure Cosmos DB NoSQL Account
- Resource health and metric alerts

