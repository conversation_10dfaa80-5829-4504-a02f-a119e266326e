// Cosmos DB Account Creation with Gremlin API
//----------------------------------------------

resource "azurerm_cosmosdb_account" "cgre" {
  //Checkov
  #checkov:skip=CKV_AZURE_100:Postponed until CMK team is available
  #checkov:skip=CKV_TF_1: We strictly use single tags for single hash
  #checkov:skip=CKV_AZURE_140: DisableLocalAuth is only allowed to be configured for SQL API account

  name                               = local.cgrename
  location                           = var.conventions.region
  resource_group_name                = var.resource_group_name
  public_network_access_enabled      = false
  is_virtual_network_filter_enabled  = true
  access_key_metadata_writes_enabled = false
  ip_range_filter                    = var.ip_range_filter
  offer_type                         = "Standard" // The only accepted type is Standard
  kind                               = "GlobalDocumentDB"
  minimal_tls_version                = var.minimal_tls_version

  // Define where data should be replicated with the failover_priority 0 specifying the primary location
  dynamic "geo_location" {
    for_each = var.failover_locations == null ? local.default_failover_locations : var.failover_locations
    content {
      location          = geo_location.value.location
      failover_priority = lookup(geo_location.value, "failover_priority", 0)
      zone_redundant    = lookup(geo_location.value, "zone_redundant", false)
    }
  }

  consistency_policy {
    consistency_level       = lookup(var.consistency_policy, "consistency_level", "BoundedStaleness")
    max_interval_in_seconds = lookup(var.consistency_policy, "consistency_level") == "BoundedStaleness" ? lookup(var.consistency_policy, "max_interval_in_seconds", 5) : null
    max_staleness_prefix    = lookup(var.consistency_policy, "consistency_level") == "BoundedStaleness" ? lookup(var.consistency_policy, "max_staleness_prefix", 100) : null
  }

  capacity {
    total_throughput_limit = var.total_throughput_limit
  }

  dynamic "backup" {
    for_each = var.backup != null ? [var.backup] : []
    content {
      type                = lookup(var.backup, "type", null)
      interval_in_minutes = lookup(var.backup, "interval_in_minutes", null)
      retention_in_hours  = lookup(var.backup, "retention_in_hours", null) == null ? (lower(var.conventions.environment) == "ppr" || lower(var.conventions.environment) == "prd" ? 720 : 168) : lookup(var.backup, "retention_in_hours", null)
      storage_redundancy  = lookup(var.backup, "type", null) == "Periodic" ? lookup(var.backup, "storage_redundancy", "Local") : null
    }
  }

  capabilities {
    name = "EnableGremlin"
  }

  dynamic "identity" {
    for_each = length(var.identity_ids) > 0 ? [1] : []
    content {
      type         = "UserAssigned"
      identity_ids = var.identity_ids
    }
  }

  key_vault_key_id      = var.key_vault_key_id
  managed_hsm_key_id    = var.managed_hsm_key_id
  default_identity_type = length(var.identity_ids) > 0 ? "UserAssignedIdentity=${var.identity_ids[0]}" : null

}
module "privateendpoint01" {
  //Checkov
  #checkov:skip=CKV_TF_1: We strictly use single tags for single hash  
  source               = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-brick-private-endpoint?ref=v1.3.0"
  conventions          = var.conventions
  resource_group_name  = var.resource_group_name
  location             = var.conventions.region
  resource_name_suffix = "${var.resource_name_suffix}-${local.resource_shortname}"
  subnet_id            = var.subnet_id
  resource_id          = azurerm_cosmosdb_account.cgre.id
  subresource_list     = var.pe_subresource_list
  wait_after           = var.wait_after

  depends_on = [ time_sleep.pe_wait ]
}

//-- Role assignment
resource "azurerm_role_assignment" "cosmosdb_operator_role_assignment" {
  count                = length(local.operator_ids)
  scope                = azurerm_cosmosdb_account.cgre.id
  role_definition_name = "Cosmos DB Operator"
  principal_id         = local.operator_ids[count.index]
}

//-- Logging
module "gremlin_logging" {
  //Checkov
  #checkov:skip=CKV_TF_1: We strictly use single tags for single hash   
  count                          = length(var.log_analytics_metrics) != 0 || length(var.log_analytics_diag_logs) != 0 ? 1 : 0
  source                         = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-brick-diag?ref=v1.1.0"
  resource_name_suffix           = var.resource_name_suffix
  conventions                    = var.conventions
  diag_target_resource_id        = azurerm_cosmosdb_account.cgre.id
  diag_loganalytics_workspace_id = var.log_analytics_workspace_id
  diag_loganalytics_diag_logs    = var.log_analytics_diag_logs
  diag_loganalytics_metrics      = var.log_analytics_metrics

  depends_on = [ time_sleep.logging_wait ]
}

//----------------------------------------------------------------- Resource health alert
module "resource_health_cgre" {
  #checkov:skip=CKV_TF_1:Not relevant, we are using tags for releases in our environment
  count                 = var.resource_health_monitoring ? 1 : 0
  source                = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-alerting//activity_log_alert?ref=v1.5.0"
  resource_health_alert = true
  conventions           = var.conventions
  alert_location        = var.resource_health_alert_location
  resource_group_name   = var.resource_group_name
  resource_name_suffix  = "cgre-resourcehealth-${var.resource_name_suffix}"

  scopes = [azurerm_cosmosdb_account.cgre.id]
}

// --------------------------------------------------------------- Default metric alerts
module "builtin_metrics_cgre" {
  //Checkov
  #checkov:skip=CKV_TF_1: We strictly use single tags for single hash
  count               = var.builtin_metric_monitoring ? 1 : 0
  source              = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-alerting//metric_alert?ref=v1.5.0"
  conventions         = var.conventions
  resource_group_name = var.resource_group_name

  metric_alerts = {
    "NormalizedRUConsumption" = {
      resource_name_suffix = "cgre-NormalizedRUConsumption-${var.resource_name_suffix}"
      scopes               = [azurerm_cosmosdb_account.cgre.id]
      description          = "Normalized RU Consumption"
      severity             = 1
      frequency            = "PT5M"
      window_size          = "PT15M"

      criteria = [
        {
          metric_namespace = "Microsoft.DocumentDB/DatabaseAccounts"
          metric_name      = "NormalizedRUConsumption"
          aggregation      = "Average"
          operator         = "GreaterThan"
          threshold        = var.alert_NormalizedRUConsumption_threshold //default 90
        }
      ]
    },
    "ServiceAvailability" = {
      resource_name_suffix = "cgre-ServiceAvailability-${var.resource_name_suffix}"
      scopes               = [azurerm_cosmosdb_account.cgre.id]
      description          = "Service Availability"
      severity             = 1
      frequency            = "PT30M"
      window_size          = "PT1H"

      criteria = [
        {
          metric_namespace = "Microsoft.DocumentDB/DatabaseAccounts"
          metric_name      = "ServiceAvailability"
          aggregation      = "Average"
          operator         = "LessThan"
          threshold        = var.alert_ServiceAvailability_threshold //default 99
        }
      ]
    }
  }
}

// Sleep objects are required to avoid resource locks on cosmosdb account
resource "time_sleep" "logging_wait" {
  depends_on = [ azurerm_cosmosdb_account.cgre ] 
  create_duration = "1s"
  destroy_duration = "180s"
}

resource "time_sleep" "pe_wait" {
  depends_on = [ azurerm_role_assignment.cosmosdb_operator_role_assignment, module.gremlin_logging, module.resource_health_cgre, module.builtin_metrics_cgre ] 
  create_duration = "1s"
  destroy_duration = "120s"
}
