formatter: markdown document

version: ""
header-from: README.md
footer-from: .config/footer.txt

sections:
  show:
    - header
    - footer
    - inputs
    - outputs
    - requirements
    - providers
    - resources

content: |-
  {{ .Header }}

  {{ .Requirements }}

  {{ .Providers }}


  ## Example for Provider configuration

  ```hcl
  {{ include "examples/07-gremlin-default/provider.tf" }}
  
  ```

  ## Example for Convention

  ```hcl
  {{ include "examples/07-gremlin-default/conventions.tf" }}
  
  ```

  ## Example for CosmosDB Account for MongoDB creation

  ```hcl
  {{ include "examples/07-gremlin-default/main.tf" }}
  
  ```


  {{ .Resources }}

  {{ .Inputs }}

  {{ .Outputs }}

  {{ .Footer }}

output:
  file: ""
  mode: inject
  template: |-
    <!-- BEGIN_TF_DOCS -->
    {{ .Content }}
    <!-- END_TF_DOCS -->
output-values:
  enabled: false

sort:
  enabled: true
  by: required

settings:
  anchor: true
  default: true
  description: true
  escape: true
  hide-empty: false
  html: true
  indent: 2
  lockfile: false
  required: true
  sensitive: true
  type: true
