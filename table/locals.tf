locals {
  resource_shortname = "ctab"
  ctabname           = lower("${local.resource_shortname}-${var.conventions.short_region}-${var.conventions.environment}-${var.conventions.project}-${var.resource_name_suffix}")

  default_failover_locations = [{
    location = var.conventions.region
  }]

  operator_ids = length(var.operator_ids) > 0 ? concat([data.azurerm_client_config.current.object_id], var.operator_ids) : [data.azurerm_client_config.current.object_id]

  // Tags
  tags = merge(
    var.conventions.tags,
    var.table_tags,
    {
      creation_mode            = "terraform",
      terraform-azurerm-cosmosdb = "v2.2.0",
    }
  )

}  