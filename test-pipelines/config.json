{"repos": [{"name": "terraform-azurerm-acr", "pipelines": ["terraform-azurerm-acr_terraformtest_PRD_01-default", "terraform-azurerm-acr_terraformtest_TST_01-default", "terraform-azurerm-acr_terraformtest_PRD_03-disabled-acr", "terraform-azurerm-acr_terraformtest_TST_03-disabled-acr", "terraform-azurerm-acr_checkov"]}, {"name": "terraform-azurerm-ai-comvision", "pipelines": ["terraform-azurerm-ai-comvision_terraformtest-TST-01-default", "terraform-azurerm-ai-comvision_terraformtest-PRD-01-default", "terraform-azurerm-ai-comvision_checkov"]}, {"name": "terraform-azurerm-ai-docintell", "pipelines": ["terraform-azurerm-ai-docintell_terraformtest-TST-01-default", "terraform-azurerm-ai-docintell_terraformtest-PRD-01-default", "terraform-azurerm-ai-docintell_terraformtest-TST-02-hsm", "terraform-azurerm-ai-docintell_terraformtest-PRD-02-hsm", "terraform-azurerm-ai-docintell_checkov"]}, {"name": "terraform-azurerm-ai-foundry", "pipelines": ["terraform-azurerm-ai-foundry_terraformtest_PRD_01-default", "terraform-azurerm-ai-foundry_terraformtest_TST_01-default", "terraform-azurerm-ai-foundry_terraformtest-TST-02-hsm", "terraform-azurerm-ai-foundry_terraformtest-PRD-02-hsm", "terraform-azurerm-ai-foundry_checkov"]}, {"name": "terraform-azurerm-ai-language", "pipelines": ["terraform-azurerm-ai-language_terraformtest-TST-01-default", "terraform-azurerm-ai-language_terraformtest-PRD-01-default", "terraform-azurerm-ai-language_terraformtest-TST-02-hsm", "terraform-azurerm-ai-language_terraformtest-PRD-02-hsm", "terraform-azurerm-ai-language_checkov"]}, {"name": "terraform-azurerm-ai-openai", "pipelines": ["terraform-azurerm-ai-openai_terraformtest-TST-01-default", "terraform-azurerm-ai-openai_terraformtest-PRD-01-default", "terraform-azurerm-ai-openai_checkov"]}, {"name": "terraform-azurerm-ai-search", "pipelines": ["terraform-azurerm-ai-search_terraformtest-TST-01-default", "terraform-azurerm-ai-search_terraformtest-PRD-01-default", "terraform-azurerm-ai-search_terraformtest-TST-02-hsm", "terraform-azurerm-ai-search_terraformtest-PRD-02-hsm", "terraform-azurerm-ai-search_checkov"]}, {"name": "terraform-azurerm-ai-services", "pipelines": ["terraform-azurerm-ai-services_terraformtest_PRD_01-default", "terraform-azurerm-ai-services_terraformtest_TST_01-default", "terraform-azurerm-ai-services_checkov"]}, {"name": "terraform-azurerm-ai-speech", "pipelines": ["terraform-azurerm-ai-speech_terraformtest-TST-01-default", "terraform-azurerm-ai-speech_terraformtest-PRD-01-default", "terraform-azurerm-ai-speech_terraformtest-TST-02-hsm", "terraform-azurerm-ai-speech_terraformtest-PRD-02-hsm", "terraform-azurerm-ai-speech_checkov"]}, {"name": "terraform-azurerm-ai-translator", "pipelines": ["terraform-azurerm-ai-translator_terraformtest-TST-01-default", "terraform-azurerm-ai-translator_terraformtest-PRD-01-default", "terraform-azurerm-ai-translator_terraformtest-TST-02-hsm", "terraform-azurerm-ai-translator_terraformtest-PRD-02-hsm", "terraform-azurerm-ai-translator_checkov"]}, {"name": "terraform-azurerm-aks", "pipelines": ["terraform-azurerm-aks_terraformtest_PRD_01-default", "terraform-azurerm-aks_terraformtest_TST_01-default", "terraform-azurerm-aks_checkov"]}, {"name": "terraform-azurerm-alerting", "pipelines": ["terraform-azurerm-alerting_Terraformtest_PRD_action_group_01-default", "terraform-azurerm-alerting_Terraformtest_TST_action_group_01-default", "terraform-azurerm-alerting_Terraformtest_TST_metric_alert-03-default", "terraform-azurerm-alerting_Terraformtest_PRD_scheduled_query_rules-05-default", "terraform-azurerm-alerting_Terraformtest_PRD_activity_log_alert-02-default", "terraform-azurerm-alerting_Terraformtest_PRD_metric_alert_03-default", "terraform-azurerm-alerting_Terraformtest_PRD_processing_rule_04-default", "terraform-azurerm-alerting_Terraformtest_TST_activity_log_alert_02-default", "terraform-azurerm-alerting_Terraformtest_TST_processing_rule_04-default", "terraform-azurerm-alerting_Terraformtest_TST_scheduled_query_rules-05-default", "terraform-azurerm-alerting_checkov"]}, {"name": "terraform-azurerm-analysisservice", "pipelines": ["terraform-azurerm-analysisservice_TerraformTest_PRD_01-default", "terraform-azurerm-analysisservice_TerraformTest_TST_01-default", "terraform-azurerm-analysisservice_checkov"]}, {"name": "terraform-azurerm-apimanagement", "pipelines": ["terraform-azurerm-apimanagement_Terraformtest_PRD_01-default", "terraform-azurerm-apimanagement_Terraformtest_TST_01-default", "terraform-azurerm-apimanagement_checkov"]}, {"name": "terraform-azurerm-app-configuration", "pipelines": ["terraform-azurerm-app-configuration_Terraformtest_PRD_01-default", "terraform-azurerm-app-configuration_Terraformtest_TST_01-default", "terraform-azurerm-app-configuration_checkov"]}, {"name": "terraform-azurerm-application-gateway", "pipelines": ["terraform-azurerm-application-gateway_Terraformtest_PRD_02-private", "terraform-azurerm-application-gateway_Terraformtest_TST_02-private", "terraform-azurerm-application-gateway_checkov"]}, {"name": "terraform-azurerm-application-insights", "pipelines": ["terraform-azurerm-application-insights_Terraformtest_PRD_01-default", "terraform-azurerm-application-insights_Terraformtest_TST_01-default", "terraform-azurerm-application-insights_Terraformtest_PRD_02-python-alfa-with-appi", "terraform-azurerm-application-insights_Terraformtest_TST_02-python-alfa-with-appi", "terraform-azurerm-application-insights_checkov"]}, {"name": "terraform-azurerm-appservice", "pipelines": ["terraform-azurerm-appservice_terraformtest-TST-01-default", "terraform-azurerm-appservice_terraformtest-PRD-01-default", "terraform-azurerm-appservice_checkov"]}, {"name": "terraform-azurerm-automation-account", "pipelines": ["terraform-azurerm-automation-account_terraformtest_PRD_02-default-runbook", "terraform-azurerm-automation-account_terraformtest_TST_02-default-runbook", "terraform-azurerm-automation-account_checkov"]}, {"name": "terraform-azurerm-brick-diag", "pipelines": ["terraform-azurerm-brick-diag_Terraformtest_TST_01-default", "terraform-azurerm-brick-diag_Terraformtest_PRD_01-default", "terraform-azurerm-brick-diag_checkov"]}, {"name": "terraform-azurerm-brick-pdns", "pipelines": ["terraform-azurerm-brick-pdns_Terraformtest_TST_01-default", "terraform-azurerm-brick-pdns_Terraformtest_PRD_01-default", "terraform-azurerm-brick-pdns_<PERSON>ov"]}, {"name": "terraform-azurerm-brick-private-endpoint", "pipelines": ["terraform-azurerm-brick-private-endpoint_Terraformtest_TST_01-default", "terraform-azurerm-brick-private-endpoint_Terraformtest_PRD_01-default", "terraform-azurerm-brick-private-endpoint_checkov"]}, {"name": "terraform-azurerm-brick-public-ip", "pipelines": ["terraform-azurerm-brick-public-ip_Terraformtest_TST_01-default", "terraform-azurerm-brick-public-ip_Terraformtest_PRD_01-default", "terraform-azurerm-brick-public-ip_<PERSON>ov"]}, {"name": "terraform-azurerm-brick-waf", "pipelines": ["terraform-azurerm-brick-waf_terraformtest_PRD_01-default", "terraform-azurerm-brick-waf_terraformtest_TST_01-default", "terraform-azurerm-brick-waf_checkov"]}, {"name": "terraform-azurerm-cassandrami", "pipelines": ["terraform-azurerm-cassandrami_Terraformtest_PRD_01-default", "terraform-azurerm-cassandrami_Terraformtest_TST_01-default", "terraform-azurerm-cassandra<PERSON>_checkov"]}, {"name": "terraform-azurerm-communicationservices", "pipelines": ["terraform-azurerm-communicationservices_Terraformtest_TST_01-default", "terraform-azurerm-communicationservices_Terraformtest_PRD_01-default", "terraform-azurerm-communicationservices_Checkov"]}, {"name": "terraform-azurerm-computegallery", "pipelines": ["terraform-azurerm-computegallery_terraformtest_PRD_02-default", "terraform-azurerm-computegallery_terraformtest_TST_02-default", "terraform-azurerm-computegallery_checkov"]}, {"name": "terraform-azurerm-container-apps", "pipelines": ["terraform-azurerm-container-apps_Terraformtest_PRD_container_apps_01-default", "terraform-azurerm-container-apps_Terraformtest_TST_container_apps_01-default", "terraform-azurerm-container-apps_Checkov"]}, {"name": "terraform-azurerm-cosmosdb", "pipelines": ["terraform-azurerm-cosmosdb_Terraformtest-PRD-02-mongodb_database-default", "terraform-azurerm-cosmosdb_Terraformtest-TST-02-mongodb_database-default", "terraform-azurerm-cosmosdb_Terraformtest-PRD-04-nosql_database-default", "terraform-azurerm-cosmosdb_Terraformtest-TST-04-nosql_database-default", "terraform-azurerm-cosmosdb_Terraformtest-PRD-06-table_database-default", "terraform-azurerm-cosmosdb_Terraformtest-TST-06-table_database-default", "terraform-azurerm-cosmosdb_Terraformtest-TST-08-gremlin_database", "terraform-azurerm-cosmosdb_Terraformtest-PRD-08-gremlin_database", "terraform-azurerm-cosmosdb_checkov"]}, {"name": "terraform-azurerm-databricks", "pipelines": ["terraform-azurerm-databricks_terraformtest-PRD-01-default", "terraform-azurerm-databricks_terraformtest-TST-01-default", "terraform-azurerm-databricks_checkov"]}, {"name": "terraform-azurerm-datafactory", "pipelines": ["terraform-azurerm-datafactory_TerraformTest-PRD-02-ah_integration_runtime", "terraform-azurerm-datafactory_TerraformTest-PRD-01-default", "terraform-azurerm-datafactory_TerraformTest-PRD-03-integration_runtime", "terraform-azurerm-datafactory_TerraformTest-PRD-04-vsts_config", "terraform-azurerm-datafactory_TerraformTest-PRD-05-ssis_IR", "terraform-azurerm-datafactory_TerraformTest-TST-01-default", "terraform-azurerm-datafactory_TerraformTest-TST-02-ah_integration_runtime2", "terraform-azurerm-datafactory_TerraformTest-TST-03-integration_runtime", "terraform-azurerm-datafactory_TerraformTest-TST-04-vsts_config", "terraform-azurerm-datafactory_TerraformTest-TST-05-ssis_IR", "terraform-azurerm-datafactory_checkov"]}, {"name": "terraform-azurerm-dce", "pipelines": ["terraform-azurerm-dce_terraformtest-PRD-standard-01-default", "terraform-azurerm-dce_terraformtest-TST-standard-01-default", "terraform-azurerm-dce_checkov"]}, {"name": "terraform-azurerm-dcr", "pipelines": ["terraform-azurerm-dcr_terraformtest-PRD-standard-01-default", "terraform-azurerm-dcr_terraformtest-TST-standard-01-default", "terraform-azurerm-dcr_checkov"]}, {"name": "terraform-azurerm-eventgrid", "pipelines": ["terraform-azurerm-eventgrid_Terraformtest_TST_01-default", "terraform-azurerm-eventgrid_Terraformtest_PRD_01-default", "terraform-azurerm-eventgrid_checkov"]}, {"name": "terraform-azurerm-eventhub", "pipelines": ["terraform-azurerm-eventhub_Terraformtest_PRD_01_default", "terraform-azurerm-eventhub_Terraformtest_PRD_02_default", "terraform-azurerm-eventhub_Terraformtest_TST_01_default", "terraform-azurerm-eventhub_Terraformtest_TST_02_default", "terraform-azurerm-eventhub_checkov"]}, {"name": "terraform-azurerm-frontdoor", "pipelines": ["terraform-azurerm-frontdoor_terraformtest_PRD_01-default", "terraform-azurerm-frontdoor_terraformtest_TST_01-default", "terraform-azurerm-frontdoor_checkov"]}, {"name": "terraform-azurerm-frontdoor-waf", "pipelines": ["terraform-azurerm-frontdoor-waf_terraformtest_PRD_01-default", "terraform-azurerm-frontdoor-waf_terraformtest_TST_01-default", "terraform-azurerm-frontdoor-waf_checkov"]}, {"name": "terraform-azurerm-key-vault", "pipelines": ["terraform-azurerm-key-vault_TerraformTest_PRD_key-vault_01-default", "terraform-azurerm-key-vault_TerraformTest_TST_key-vault_01-default", "terraform-azurerm-key-vault_TerraformTest_PRD_key-vault_02-certificate", "terraform-azurerm-key-vault_TerraformTest_PRD_key-vault_03-key", "terraform-azurerm-key-vault_TerraformTest_PRD_key-vault_04-secret", "terraform-azurerm-key-vault_TerraformTest_TST_key-vault_02-certificate", "terraform-azurerm-key-vault_TerraformTest_TST_key-vault_03-key", "terraform-azurerm-key-vault_TerraformTest_TST_key-vault_04-secret", "terraform-azurerm-key-vault_terraformtest_PRD_key-vault_05-keypair", "terraform-azurerm-key-vault_terraformtest_TST_key-vault_05-keypair", "terraform-azurerm-key-vault_checkov"]}, {"name": "terraform-azurerm-lb", "pipelines": ["terraform-azurerm-lb_Terraformtest_TST_01-default", "terraform-azurerm-lb_Terraformtest_PRD_01-default", "terraform-azurerm-lb_<PERSON><PERSON>"]}, {"name": "terraform-azurerm-linux-function-app", "pipelines": ["terraform-azurerm-linux-function-app_Terraformtest_PRD_01-example", "terraform-azurerm-linux-function-app_Terraformtest_TST_01-example", "terraform-azurerm-linux-function-app-checkov"]}, {"name": "terraform-azurerm-linux-web-app", "pipelines": ["terraform-azurerm-linux-web-app_terraformtest_PRD_01-default", "terraform-azurerm-linux-web-app_terraformtest_PRD_02-webapp_from_image", "terraform-azurerm-linux-web-app_terraformtest_TST_01-default", "terraform-azurerm-linux-web-app_terraformtest_TST_02-webapp_from_image", "terraform-azurerm-linux-web-app-checkov"]}, {"name": "terraform-azurerm-load-testing", "pipelines": ["terraform-azurerm-load-testing_Terraformtest_PRD_01-default", "terraform-azurerm-load-testing_Terraformtest_TST_01-default", "terraform-azurerm-load-testing_Terraformtest_PRD_02-specified-identity-parameter", "terraform-azurerm-load-testing_Terraformtest_TST_02-specified-identity-parameter", "terraform-azurerm-load-testing_checkov"]}, {"name": "terraform-azurerm-loganalytics", "pipelines": ["terraform-azurerm-loganalytics_terraformtest_PRD_01-default", "terraform-azurerm-loganalytics_terraformtest_TST_01-default", "terraform-azurerm-loganalytics-terraformtest-PRD-03-custom_table", "terraform-azurerm-loganalytics-terraformtest-TST-03-custom_table", "terraform-azurerm-loganalytics_checkov"]}, {"name": "terraform-azurerm-logicapp", "pipelines": ["terraform-azurerm-logicapp_Terraformtest-PRD-standard-01-default", "terraform-azurerm-logicapp_Terraformtest-TST-standard-01-default", "terraform-azurerm-logicapp_Terraformtest-PRD-workflow-02-workflow", "terraform-azurerm-logicapp_Terraformtest-TST-workflow-02-workflow", "terraform-azurerm-logicapp_checkov"]}, {"name": "terraform-azurerm-machine-learning", "pipelines": ["terraform-azurerm-machine-learning_terraformtest_PRD_01-default", "terraform-azurerm-machine-learning_terraformtest_TST_01-default", "terraform-azurerm-machine-learning_checkov"]}, {"name": "terraform-azurerm-managed-disk", "pipelines": ["terraform-azurerm-managed-disk_Terraformtest_PRD_diskaccess_02-daccmdsk", "terraform-azurerm-managed-disk_Terraformtest_TST_diskaccess_02-daccmdsk", "terraform-azurerm-managed-disk_checkov"]}, {"name": "terraform-azurerm-mssql", "pipelines": ["terraform-azurerm-mssql_Terraformtest_PRD_mssql_server_01-default", "terraform-azurerm-mssql_Terraformtest_PRD_mssql_database_02-default", "terraform-azurerm-mssql_Terraformtest_PRD_mssql_database_03-default", "terraform-azurerm-mssql_Terraformtest_TST_mssql_server_01-default", "terraform-azurerm-mssql_Terraformtest_TST_mssql_database_02-default", "terraform-azurerm-mssql_Terraformtest_TST_mssql_database_03-serverless", "terraform-azurerm-mssql_Terraformtest-TST-04-default", "terraform-azurerm-mssql_Terraformtest-PRD-04-default", "terraform-azurerm-mssql_checkov"]}, {"name": "terraform-azurerm-nesg", "pipelines": ["terraform-azurerm-nesg_Terraformtest_TST_01-default", "terraform-azurerm-nesg_Terraformtest_PRD_01-default", "terraform-azurerm-nesg_<PERSON>ov"]}, {"name": "terraform-azurerm-nesr", "pipelines": ["terraform-azurerm-nesr_Terraformtest_TST_01-default", "terraform-azurerm-nesr_Terraformtest_PRD_01-default", "terraform-azurerm-nesr_<PERSON><PERSON>"]}, {"name": "terraform-azurerm-postgresql", "pipelines": ["terraform-azurerm-postgresql_terraformtest_PRD_02-default", "terraform-azurerm-postgresql_terraformtest_TST_02-default", "terraform-azurerm-postgresql_checkov"]}, {"name": "terraform-azurerm-purview", "pipelines": ["terraform-azurerm-purview_Terraformtest_PRD_01-default", "terraform-azurerm-purview_Terraformtest_TST_01-default", "terraform-azurerm-purview_Checkov"]}, {"name": "terraform-azurerm-recovery-services-vault", "pipelines": ["terraform-azurerm-recovery-services-vault_Terraformtest-PRD-01-default", "terraform-azurerm-recovery-services-vault_Terraformtest-TST-01-default", "terraform-azurerm-recovery-services-vault_Terraformtest-PRD-02-vm-with-backup", "terraform-azurerm-recovery-services-vault_Terraformtest-TST-02-vm-with-backup", "terraform-azurerm-recovery-services-vault-checkov"]}, {"name": "terraform-azurerm-redis-cache", "pipelines": ["terraform-azurerm-redis-cache_Terraform_PRD_01-default", "terraform-azurerm-redis-cache_Terraform_TST_01-default", "terraform-azurerm-redis-cache_checkov"]}, {"name": "terraform-azurerm-relay", "pipelines": ["terraform-azurerm-relay_Terraformtest_PRD_relay_hybrid_connection_01-default", "terraform-azurerm-relay_Terraformtest_TST_relay_hybrid_connection_01-default", "terraform-azurerm-relay_Terraformtest_PRD_relay_namespace_01-default", "terraform-azurerm-relay_Terraformtest_TST_relay_namespace_01-default", "terraform-azurerm-relay_checkov"]}, {"name": "terraform-azurerm-rg", "pipelines": ["terraform-azurerm-rg_Terraformtest_TST_01-default", "terraform-azurerm-rg_Terraformtest_PRD_01-default", "terraform-azurerm-rg_<PERSON>ov"]}, {"name": "terraform-azurerm-rhel-vm", "pipelines": ["terraform-azurerm-rhel-vm_Terraformtest-PRD-01-default", "terraform-azurerm-rhel-vm_Terraformtest-TST-01-default", "terraform-azurerm-rhel-vm_Terraformtest_PRD-02-compute_gallery_image", "terraform-azurerm-rhel-vm_Terraformtest_TST-02-compute_gallery_image", "terraform-azurerm-rhel-vm_checkov"]}, {"name": "terraform-azurerm-rhel-vm-scale-set", "pipelines": ["terraform-azurerm-rhel-vm-scale-set_Terraformtest_PRD_01-default", "terraform-azurerm-rhel-vm-scale-set_Terraformtest_TST_01-default", "terraform-azurerm-rhel-vm-scale-set_Terraformtest_PRD_02-compute-gallery-image", "terraform-azurerm-rhel-vm-scale-set_Terraformtest_TST_02-compute-gallery-image", "terraform-azurerm-rhel-vm-scale-set_Terraformtest_PRD_04-flexible", "terraform-azurerm-rhel-vm-scale-set_Terraformtest_TST_04-default", "terraform-azurerm-rhel-vm-scale-set_checkov"]}, {"name": "terraform-azurerm-route", "pipelines": ["terraform-azurerm-route_Terraformtest_TST_01-default", "terraform-azurerm-route_Terraformtest_PRD_01-default", "terraform-azurerm-route_<PERSON>ov"]}, {"name": "terraform-azurerm-rtbl", "pipelines": ["terraform-azurerm-rtbl_Terraformtest_TST_01-default", "terraform-azurerm-rtbl_Terraformtest_PRD_01-default", "terraform-azurerm-rtbl_Checkov"]}, {"name": "terraform-azurerm-servicebus", "pipelines": ["terraform-azurerm-servicebus_Terraformtest_PRD_sb_namespace_01-default", "terraform-azurerm-servicebus_Terraformtest_TST_sb_namespace_01-default", "terraform-azurerm-servicebus_Terraformtest_PRD_sb_queue_02-default", "terraform-azurerm-servicebus_Terraformtest_TST_sb_queue_02-default", "terraform-azurerm-servicebus_Terraformtest_PRD_sb_topic_03-default", "terraform-azurerm-servicebus_Terraformtest_TST_sb_topic_03-default", "terraform-azurerm-servicebus_checkov"]}, {"name": "terraform-azurerm-sqlmi", "pipelines": ["terraform-azurerm-sqlmi_Terraformtest_PRD_sqmd_database_05-logging", "terraform-azurerm-sqlmi_Terraformtest_TST_sqmd_database_05-logging", "terraform-azurerm-sqlmi_checkov"]}, {"name": "terraform-azurerm-storageaccount", "pipelines": ["terraform-azurerm-storageaccount_Terraformtest_PRD_01-default", "terraform-azurerm-storageaccount_Terraformtest_PRD_02-adlsgen2", "terraform-azurerm-storageaccount_Terraformtest_PRD_03-premium-smb-share", "terraform-azurerm-storageaccount_Terraformtest_PRD_04-premium-nfs-share", "terraform-azurerm-storageaccount_Terraformtest_PRD_05-table", "terraform-azurerm-storageaccount_Terraformtest_PRD_06-queue", "terraform-azurerm-storageaccount_Terraformtest_TST_01-default", "terraform-azurerm-storageaccount_Terraformtest_TST_02-adlsgen2", "terraform-azurerm-storageaccount_Terraformtest_TST_03-premium-smb-share", "terraform-azurerm-storageaccount_Terraformtest_TST_04-premium-nfs-share", "terraform-azurerm-storageaccount_Terraformtest_TST_05-table", "terraform-azurerm-storageaccount_Terraformtest_TST_06-queue", "terraform-azurerm-storageaccount_checkov"]}, {"name": "terraform-azurerm-streamanalytics", "pipelines": ["terraform-azurerm-streamanalytics_terraformtest_PRD_01-new-cluster", "terraform-azurerm-streamanalytics_terraformtest_PRD_03-no-cluster", "terraform-azurerm-streamanalytics_terraformtest_TST_01-new-cluster", "terraform-azurerm-streamanalytics_terraformtest_TST_03-no-cluster", "terraform-azurerm-streamanalytics_checkov"]}, {"name": "terraform-azurerm-subnet", "pipelines": ["terraform-azurerm-subnet_terraformtest-TST-01-default", "terraform-azurerm-subnet_terraformtest-PRD-01-default", "terraform-azurerm-subnet_checkov"]}, {"name": "terraform-azurerm-synapse-workspace", "pipelines": ["terraform-azurerm-synapse-workspace_TerraformTest_PRD_01-default", "terraform-azurerm-synapse-workspace_TerraformTest_PRD-02-github_repo", "terraform-azurerm-synapse-workspace_TerraformTest_TST_01-default", "terraform-azurerm-synapse-workspace_TerraformTest_TST_02-github_repo", "terraform-azurerm-synapse-workspace_checkov"]}, {"name": "terraform-azurerm-trafficmanager", "pipelines": ["terraform-azurerm-trafficmanager_terraformtest_PRD_01-default", "terraform-azurerm-trafficmanager_terraformtest_TST_01-default", "terraform-azurerm-trafficmanager_checkov"]}, {"name": "terraform-azurerm-vnic", "pipelines": ["terraform-azurerm-vnic_Terraformtest_TST_01-default", "terraform-azurerm-vnic_Terraformtest_PRD_01-default", "terraform-azurerm-v<PERSON>_<PERSON><PERSON>"]}, {"name": "terraform-azurerm-windows-function-app", "pipelines": ["terraform-azurerm-windows-function-app_terraformtest-PRD-01-default", "terraform-azurerm-windows-function-app_terraformtest-TST-01-default", "terraform-azurerm-windows-function-app_<PERSON>ov"]}, {"name": "terraform-azurerm-windows-vm", "pipelines": ["terraform-azurerm-windows-vm_terraformtest_PRD_01-default", "terraform-azurerm-windows-vm_terraformtest_TST_01-default", "terraform-azurerm-windows-vm_terraformtest_PRD-02-compute_gallery_image", "terraform-azurerm-windows-vm_terraformtest_TST_02-compute_gallery_image", "terraform-azurerm-windows-vm_checkov"]}, {"name": "terraform-azurerm-windows-vm-scale-set", "pipelines": ["terraform-azurerm-windows-vm-scale-set_Terraformtest_PRD_01-default", "terraform-azurerm-windows-vm-scale-set_Terraformtest_TST_01-default", "terraform-azurerm-windows-vm-scale-set_Terraformtest_PRD_02-compute_gallery_image", "terraform-azurerm-windows-vm-scale-set_Terraformtest_TST_02-compute_gallery_image", "terraform-azurerm-windows-vm-scale-set_Terraformtest_TST_04-flexible", "terraform-azurerm-windows-vm-scale-set_Terraformtest_PRD_04-flexible", "terraform-azurerm-windows-vm-scale-set_checkov"]}, {"name": "terraform-azurerm-windows-web-app", "pipelines": ["terraform-azurerm-windows-web-app_terraformtest_TST_01-default", "terraform-azurerm-windows-web-app_terraformtest_TST_02-webapp_from_image", "terraform-azurerm-windows-web-app_terraformtest_PRD_01-default", "terraform-azurerm-windows-web-app_terraformtest_PRD-02-webapp_from_image", "terraform-azurerm-windows-web-app_checkov"]}]}