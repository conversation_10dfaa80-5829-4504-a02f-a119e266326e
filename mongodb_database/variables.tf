variable "conventions" {
  description = "(Required) terraform-conventions module"
}

variable "account_name" {
  type = string
  description = "(Required) The name of the Cosmos DB Mongo Database to create the table within. Changing this forces a new resource to be created."
}

variable "resource_group_name" {
  type = string
  description = "(Required) Specifies the name of the resource group."
}

variable "databases" {
  type = map(object({
    throughput     = optional(number)
    max_throughput = optional(number)
    collections = list(object({
      name           = string
      shard_key      = string
      throughput     = optional(number)
      max_throughput = optional(number)
      index_unique   = optional(bool)
    }))
  }))
  default     = {}
  description = <<-EOT
  List of databases
    throughput - (Optional) The throughput of the MongoDB database (RU/s). Must be set in increments of 100. The minimum value is 400. This must be set upon database creation otherwise it cannot be updated without a manual terraform destroy-apply.
    max_throughput - (Optional) The maximum throughput of the MongoDB database (RU/s). Must be between 1,000 and 1,000,000. Must be set in increments of 1,000. Conflicts with throughput.
    Collections:
      name - (Required) Specifies the name of the Cosmos DB Mongo Collection. Changing this forces a new resource to be created.
      shard_key - (Optional) The name of the key to partition on for sharding. There must not be any other unique index keys. Changing this forces a new resource to be created.
      throughput - (Optional) The throughput of the MongoDB collection (RU/s). Must be set in increments of 100. The minimum value is 400. This must be set upon database creation otherwise it cannot be updated without a manual terraform destroy-apply.
      max_throughput - (Optional) The maximum throughput of the MongoDB collection (RU/s). Must be between 1,000 and 1,000,000. Must be set in increments of 1,000. Conflicts with throughput.
      index_unique - (Optional) Is the index unique or not? Defaults to false.
  EOT
}


