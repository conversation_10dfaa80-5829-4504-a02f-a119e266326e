// Local values
locals {
  // Locals for collections
  collections = flatten([
    for db_key, db in var.databases : [
      for col in db.collections : {
        name           = col.name
        database       = db_key
        shard_key      = col.shard_key
        throughput     = col.throughput
        max_throughput = col.max_throughput
        index_unique   = col.index_unique
      }
    ]
  ])
  //Tags
  //tags are not applicable here
}