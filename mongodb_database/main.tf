// Database and collection creation
//----------------------------------
resource "azurerm_cosmosdb_mongo_database" "main" {
  //Checkov
  #checkov:skip=CKV_TF_1: We strictly use single tags for single hash  
  for_each = var.databases

  name                = each.key
  resource_group_name = var.resource_group_name
  account_name        = var.account_name
  throughput          = each.value.throughput

  dynamic "autoscale_settings" {
    for_each = each.value.max_throughput != null ? [each.value.max_throughput] : []
    content {
      max_throughput = autoscale_settings.value
    }
  }
}

resource "azurerm_cosmosdb_mongo_collection" "coll" {
  //Checkov
  #checkov:skip=CKV_TF_1: We strictly use single tags for single hash  
  for_each = { for col in local.collections : col.name => col }

  name                = each.value.name
  resource_group_name = var.resource_group_name
  account_name        = var.account_name
  database_name       = each.value.database
  shard_key           = each.value.shard_key
  throughput          = each.value.throughput

  index {
    keys   = ["_id"]
    unique = each.value.index_unique
  }

  dynamic "autoscale_settings" {
    for_each = each.value.max_throughput != null ? [each.value.max_throughput] : []
    content {
      max_throughput = autoscale_settings.value
    }
  }

  lifecycle {
    ignore_changes = [index, default_ttl_seconds]
  }

  depends_on = [azurerm_cosmosdb_mongo_database.main]
}


