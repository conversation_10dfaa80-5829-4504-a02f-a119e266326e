## Name of the module
Azure Cosmos DB MongoDB Database

shortname: -

terraform resource: azurerm_cosmosdb_mongo_database, azurerm_cosmosdb_mongo_collection

## Short description of the module
This Terraform module deploys an Azure Cosmos DB MongoDB database and collection.

## Detailed description on Confluence
[Azure Cosmos DB ](https://confluence.otpbank.hu/x/7YN6Kw) 

## Release notes – changes in the current and previous versions
[CHANGELOG.md](../CHANGELOG.md)

## Terraform version compatibility
Terraform >= 1.7.4

## Necessary Terraform providers, and compatibility to provider versions
provider registry.terraform.io/hashicorp/azurerm >= 4.2.0

## Resources generated by the module
- MongoDB database and collection

