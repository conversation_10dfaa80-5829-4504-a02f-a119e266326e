variable "conventions" {
  description = "(Required) terraform-conventions module"
}

variable "account_name" {
  type = string
  description = "(Required) The name of the Cosmos DB SQL Database to create the table within. Changing this forces a new resource to be created."
}

variable "resource_group_name" {
  type = string
  description = "(Required) Specifies the name of the resource group."
}

variable "sql_dbs" {
  type = map(object({
    db_name           = string
    db_throughput     = optional(number)
    db_max_throughput = optional(number)
  }))
  description = "(Required) Cosmos DB SQL DBs"
  default     = {}
}

variable "sql_db_containers" {
  type = map(object({
    container_name           = string
    db_name                  = string
    partition_key_paths      = list(string)
    partition_key_version    = number
    container_throughput     = optional(number)
    container_max_throughput = optional(number)
    default_ttl              = number
    analytical_storage_ttl   = number
    indexing_policy_settings = object({
      sql_indexing_mode = optional(string)
      sql_included_path = optional(string)
      sql_excluded_path = optional(string)
      composite_indexes = optional(map(object({
        indexes = set(object({
          path  = string
          order = string
        }))
      })))
      spatial_indexes = optional(map(object({
        path = string
      })))
    })
    sql_unique_key = list(string)
    conflict_resolution_policy = object({
      mode      = string
      path      = string
      procedure = string
    })
  }))
  description = "(Optional) List of Cosmos DB SQL Containers"
  default     = {}
}