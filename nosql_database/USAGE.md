<!-- BEGIN_TF_DOCS -->
## Name of the module
Azure Cosmos DB NoSQL Database

shortname: -

terraform resource: azurerm\_cosmosdb\_sql\_database, azurerm\_cosmosdb\_sql\_container

## Short description of the module
This Terraform module deploys an Azure Cosmos DB NoSQL database and container.

## Detailed description on Confluence
[Azure Cosmos DB ](https://confluence.otpbank.hu/x/7YN6Kw)

## Release notes – changes in the current and previous versions
[CHANGELOG.md](../CHANGELOG.md)

## Terraform version compatibility
Terraform >= 1.7.4

## Necessary Terraform providers, and compatibility to provider versions
provider registry.terraform.io/hashicorp/azurerm >= 4.2.0

## Resources generated by the module
- MongoDB database and collection

## Requirements

The following requirements are needed by this module:

- <a name="requirement_azurerm"></a> [azurerm](#requirement\_azurerm) (>= 4.2.0)

## Providers

The following providers are used by this module:

- <a name="provider_azurerm"></a> [azurerm](#provider\_azurerm) (>= 4.2.0)


## Example for Provider configuration

```hcl
provider "azurerm" {
  features {
    resource_group {
      prevent_deletion_if_contains_resources = false
    }
  }
  storage_use_azuread = true
}

provider "time" {
}

#required_providers - Make sure that you are using versions, which available on https://az-nexus.otpbank.hu/repository/nexus-terraform-provider/.
terraform {
  required_providers {
    azurerm = {
      source  = "hashicorp/azurerm"
      version = "4.10.0"
    }
    time = {
      source  = "hashicorp/time"
      version = "0.11.2"
    }
  }
}

```

## Example for Convention

```hcl
module "conventions" {
  //Checkov  
  #checkov:skip=CKV_TF_1: We strictly use single tags for single hash      
  source      = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-base//conventions?ref=v2.0.3"
  cloud       = var.cloud
  environment = var.environment
  project     = var.project
  region      = var.region
  subsidiary  = var.subsidiary
  tags = {
    "OwnerOU"      = "ccoe"
    "OwnerContact" = var.owner //In the blueprint change the OwnerContact tag to the owner of the solution and remove variable "owner" {}.
    "Criticality"  = "Low"
  }
}

```

## Example for Nosql Database creation

```hcl
locals {
  resource_name_suffix = "cosmosdbnosqldb-04-t10"
}

module "rg01" {
  //Checkov
  #checkov:skip=CKV_TF_1: We strictly use single tags for single hash  
  source               = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-rg?ref=v1.2.0"
  conventions          = module.conventions
  resource_name_suffix = local.resource_name_suffix
}

module "nosql" {

  // Create Cosmos DB Account first
  // source = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-cosmosdb//nosql?ref=v2.2.0"
  source               = "../../../nosql"
  conventions          = module.conventions
  resource_group_name  = module.rg01.rgrp.name
  resource_name_suffix = local.resource_name_suffix
  subnet_id            = data.azurerm_subnet.sn_privateendpoint.id
  wait_after           = 120
  ip_range_filter      = ["*************", "************", "***********", "************", "*************"] //example IP list allows requests from the Azure portal 


  // Optional: Limit the total throughput providisoned on your Azure Cosmos DB Account
  // total_throughput_limit = 1000
  consistency_policy = {
    consistency_level       = "BoundedStaleness" // can be either BoundedStaleness, Eventual, Session, Strong or ConsistentPrefix
    max_interval_in_seconds = 300
    max_staleness_prefix    = 100000
  }

  // Example for a standalone deployment
  failover_locations = [
    {
      location          = "westeurope"
      failover_priority = 0
      zone_redundant    = false
    }
  ]

  backup = {
    type                = "Periodic"
    interval_in_minutes = 240
    retention_in_hours  = 8
  }

  //Diagnostic settings
  log_analytics_workspace_id = module.conventions.log_analytics_workspace_id
  //Example with all metric types:
  log_analytics_metrics = ["AllMetrics"]
  //Example with list of log types:
  //log_analytics_diag_logs    = ["ControlPlaneRequests", "DataPlaneRequests", "PartitionKeyRUConsumption", "PartitionKeyStatistics", "QueryRuntimeStatistics", "TableApiRequests"]
  //Example with all log types:
  log_analytics_diag_logs = ["AllLogs"]
}

module "nosql_database" {
  // source = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-cosmosdb//nosql_database?ref=v2.2.0"
  source              = "../../"
  conventions         = module.conventions
  resource_group_name = module.rg01.rgrp.name
  account_name        = module.nosql.cnsq.name

  sql_dbs = {
    database_test1 = {
      db_name           = "database_autoscale"
      db_max_throughput = 1000 // Example with autoscale option. Check documentation to understand how throughput can be configured: https://learn.microsoft.com/en-us/azure/cosmos-db/limit-total-account-throughput
    }
    database_test2 = {
      db_name       = "database_test2"
      db_throughput = 500 // This must be set upon database creation otherwise it cannot be updated without a manual terraform destroy-apply. Switching between autoscale and manual throughput is not supported via Terraform and must be completed via the Azure Portal and refreshed.
    }
    database_test3 = {
      db_name       = "database_test3"
      db_throughput = 500
    }
  }

  sql_db_containers = {
    container1 = {
      container_name        = "container1"
      db_name               = "database_autoscale"
      partition_key_paths    = ["/container/id"]
      partition_key_version = 2
      container_throughput  = 400 //Minimum 100. Set in increments of 100.
      #container_max_throughput = 500
      default_ttl            = null
      analytical_storage_ttl = null
      indexing_policy_settings = {
        sql_indexing_mode = "consistent"
        sql_included_path = "/*"
        sql_excluded_path = null
        composite_indexes = {
          compositeindexone = {
            indexes = [
              {
                path  = "/container/name"
                order = "Ascending"
              },
              {
                path  = "/container/id"
                order = "Ascending"
              }
            ]
          }
        }
        spatial_indexes = {
          spatialindexone = {
            path = "/*"
          }
        }
      }
      sql_unique_key             = ["/container/id"]
      conflict_resolution_policy = null
    }
    container2 = {
      container_name        = "container2"
      db_name               = "database_test2"
      partition_key_paths    = ["/container/id"]
      partition_key_version = 2
      container_throughput  = 400
      //container_max_throughput = null //Optional
      default_ttl            = null
      analytical_storage_ttl = null
      indexing_policy_settings = {
        sql_included_path = "/*"
      }
      sql_unique_key             = []
      conflict_resolution_policy = null
    }
    container3 = {
      container_name        = "container3"
      db_name               = "database_test3"
      partition_key_paths    = ["/container/id"]
      partition_key_version = 2
      container_throughput  = 400 //Minimum 100. Set in increments of 100.
      //container_max_throughput = null //Optional
      default_ttl            = null
      analytical_storage_ttl = null
      indexing_policy_settings = {
        sql_indexing_mode = "consistent"
        sql_included_path = "/*"
        sql_excluded_path = null
        composite_indexes = {
          compositeindexone = {
            indexes = [
              {
                path  = "/container/name"
                order = "Ascending"
              },
              {
                path  = "/container/id"
                order = "Ascending"
              }
            ]
          }
        }
        spatial_indexes = {
          spatialindexone = {
            path = "/*"
          }
        }
      }
      sql_unique_key             = ["/container/id"]
      conflict_resolution_policy = null
    }
  }

}


```


## Resources

The following resources are used by this module:

- [azurerm_cosmosdb_sql_container.cont](https://registry.terraform.io/providers/hashicorp/azurerm/latest/docs/resources/cosmosdb_sql_container) (resource)
- [azurerm_cosmosdb_sql_database.main](https://registry.terraform.io/providers/hashicorp/azurerm/latest/docs/resources/cosmosdb_sql_database) (resource)

## Required Inputs

The following input variables are required:

### <a name="input_account_name"></a> [account\_name](#input\_account\_name)

Description: (Required) The name of the Cosmos DB SQL Database to create the table within. Changing this forces a new resource to be created.

Type: `string`

### <a name="input_conventions"></a> [conventions](#input\_conventions)

Description: (Required) terraform-conventions module

Type: `any`

### <a name="input_resource_group_name"></a> [resource\_group\_name](#input\_resource\_group\_name)

Description: (Required) Specifies the name of the resource group.

Type: `string`

## Optional Inputs

The following input variables are optional (have default values):

### <a name="input_sql_db_containers"></a> [sql\_db\_containers](#input\_sql\_db\_containers)

Description: (Optional) List of Cosmos DB SQL Containers

Type:

```hcl
map(object({
    container_name           = string
    db_name                  = string
    partition_key_paths      = list(string)
    partition_key_version    = number
    container_throughput     = optional(number)
    container_max_throughput = optional(number)
    default_ttl              = number
    analytical_storage_ttl   = number
    indexing_policy_settings = object({
      sql_indexing_mode = optional(string)
      sql_included_path = optional(string)
      sql_excluded_path = optional(string)
      composite_indexes = optional(map(object({
        indexes = set(object({
          path  = string
          order = string
        }))
      })))
      spatial_indexes = optional(map(object({
        path = string
      })))
    })
    sql_unique_key = list(string)
    conflict_resolution_policy = object({
      mode      = string
      path      = string
      procedure = string
    })
  }))
```

Default: `{}`

### <a name="input_sql_dbs"></a> [sql\_dbs](#input\_sql\_dbs)

Description: (Required) Cosmos DB SQL DBs

Type:

```hcl
map(object({
    db_name           = string
    db_throughput     = optional(number)
    db_max_throughput = optional(number)
  }))
```

Default: `{}`

## Outputs

The following outputs are exported:

### <a name="output_cnsq"></a> [cnsq](#output\_cnsq)

Description: nosql database

## Contributing

* If you think you've found a bug in the code or you have a question regarding
  the usage of this module, please reach out to <NAME_EMAIL>
* Contributions to this project are welcome: if you want to add a feature or a
  fix a bug, please do so by opening a Pull Request in this repository.
  In case of feature contribution, we kindly ask you to send a mail to
  discuss it beforehand.
<!-- END_TF_DOCS -->