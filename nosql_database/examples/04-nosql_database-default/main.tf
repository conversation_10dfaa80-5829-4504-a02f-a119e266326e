locals {
  resource_name_suffix = "cosmosdbnosqldb-04-t10"
}

module "rg01" {
  //Checkov
  #checkov:skip=CKV_TF_1: We strictly use single tags for single hash  
  source               = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-rg?ref=v1.2.0"
  conventions          = module.conventions
  resource_name_suffix = local.resource_name_suffix
}

module "nosql" {

  // Create Cosmos DB Account first
  // source = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-cosmosdb//nosql?ref=v2.2.0"
  source               = "../../../nosql"
  conventions          = module.conventions
  resource_group_name  = module.rg01.rgrp.name
  resource_name_suffix = local.resource_name_suffix
  subnet_id            = data.azurerm_subnet.sn_privateendpoint.id
  wait_after           = 120
  ip_range_filter      = ["*************", "************", "***********", "************", "*************"] //example IP list allows requests from the Azure portal 


  // Optional: Limit the total throughput providisoned on your Azure Cosmos DB Account
  // total_throughput_limit = 1000
  consistency_policy = {
    consistency_level       = "BoundedStaleness" // can be either BoundedStaleness, Eventual, Session, Strong or ConsistentPrefix
    max_interval_in_seconds = 300
    max_staleness_prefix    = 100000
  }

  // Example for a standalone deployment
  failover_locations = [
    {
      location          = "westeurope"
      failover_priority = 0
      zone_redundant    = false
    }
  ]

  backup = {
    type                = "Periodic"
    interval_in_minutes = 240
    retention_in_hours  = 8
  }

  //Diagnostic settings
  log_analytics_workspace_id = module.conventions.log_analytics_workspace_id
  //Example with all metric types:
  log_analytics_metrics = ["AllMetrics"]
  //Example with list of log types:
  //log_analytics_diag_logs    = ["ControlPlaneRequests", "DataPlaneRequests", "PartitionKeyRUConsumption", "PartitionKeyStatistics", "QueryRuntimeStatistics", "TableApiRequests"]
  //Example with all log types:
  log_analytics_diag_logs = ["AllLogs"]
}

module "nosql_database" {
  // source = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-cosmosdb//nosql_database?ref=v2.2.0"
  source              = "../../"
  conventions         = module.conventions
  resource_group_name = module.rg01.rgrp.name
  account_name        = module.nosql.cnsq.name

  sql_dbs = {
    database_test1 = {
      db_name           = "database_autoscale"
      db_max_throughput = 1000 // Example with autoscale option. Check documentation to understand how throughput can be configured: https://learn.microsoft.com/en-us/azure/cosmos-db/limit-total-account-throughput
    }
    database_test2 = {
      db_name       = "database_test2"
      db_throughput = 500 // This must be set upon database creation otherwise it cannot be updated without a manual terraform destroy-apply. Switching between autoscale and manual throughput is not supported via Terraform and must be completed via the Azure Portal and refreshed.
    }
    database_test3 = {
      db_name       = "database_test3"
      db_throughput = 500
    }
  }

  sql_db_containers = {
    container1 = {
      container_name        = "container1"
      db_name               = "database_autoscale"
      partition_key_paths    = ["/container/id"]
      partition_key_version = 2
      container_throughput  = 400 //Minimum 100. Set in increments of 100.
      #container_max_throughput = 500
      default_ttl            = null
      analytical_storage_ttl = null
      indexing_policy_settings = {
        sql_indexing_mode = "consistent"
        sql_included_path = "/*"
        sql_excluded_path = null
        composite_indexes = {
          compositeindexone = {
            indexes = [
              {
                path  = "/container/name"
                order = "Ascending"
              },
              {
                path  = "/container/id"
                order = "Ascending"
              }
            ]
          }
        }
        spatial_indexes = {
          spatialindexone = {
            path = "/*"
          }
        }
      }
      sql_unique_key             = ["/container/id"]
      conflict_resolution_policy = null
    }
    container2 = {
      container_name        = "container2"
      db_name               = "database_test2"
      partition_key_paths    = ["/container/id"]
      partition_key_version = 2
      container_throughput  = 400
      //container_max_throughput = null //Optional
      default_ttl            = null
      analytical_storage_ttl = null
      indexing_policy_settings = {
        sql_included_path = "/*"
      }
      sql_unique_key             = []
      conflict_resolution_policy = null
    }
    container3 = {
      container_name        = "container3"
      db_name               = "database_test3"
      partition_key_paths    = ["/container/id"]
      partition_key_version = 2
      container_throughput  = 400 //Minimum 100. Set in increments of 100.
      //container_max_throughput = null //Optional
      default_ttl            = null
      analytical_storage_ttl = null
      indexing_policy_settings = {
        sql_indexing_mode = "consistent"
        sql_included_path = "/*"
        sql_excluded_path = null
        composite_indexes = {
          compositeindexone = {
            indexes = [
              {
                path  = "/container/name"
                order = "Ascending"
              },
              {
                path  = "/container/id"
                order = "Ascending"
              }
            ]
          }
        }
        spatial_indexes = {
          spatialindexone = {
            path = "/*"
          }
        }
      }
      sql_unique_key             = ["/container/id"]
      conflict_resolution_policy = null
    }
  }

}

