<!-- BEGIN_TF_DOCS -->
## Name of the module
Azure Cosmos DB MongoDB Account

shortname: cmdb

terraform resource: azurerm\_cosmosdb\_account

## Short description of the module
This Terraform module deploys an Azure Cosmos DB MongoDB Account.

## Detailed description on Confluence
[Azure Cosmos DB ](https://confluence.otpbank.hu/x/7YN6Kw)

## Release notes – changes in the current and previous versions
[CHANGELOG.md](../CHANGELOG.md)

## Terraform version compatibility
Terraform >= 1.7.4

## Necessary Terraform providers, and compatibility to provider versions
provider registry.terraform.io/hashicorp/azurerm >= 4.2.0
provider registry.terraform.io/hashicorp/time >= 0.9.1

## Resources generated by the module
- Azure Cosmos DB MongoDB Account
- Resource health and metric alerts

## Requirements

The following requirements are needed by this module:

- <a name="requirement_azurerm"></a> [azurerm](#requirement\_azurerm) (>= 4.2.0)

- <a name="requirement_time"></a> [time](#requirement\_time) (>=0.9.1)

## Providers

The following providers are used by this module:

- <a name="provider_azurerm"></a> [azurerm](#provider\_azurerm) (>= 4.2.0)

- <a name="provider_time"></a> [time](#provider\_time) (>=0.9.1)


## Example for Provider configuration

```hcl
provider "azurerm" {
  features {
    resource_group {
      prevent_deletion_if_contains_resources = false
    }
  }
  storage_use_azuread = true
}

provider "time" {
}

#required_providers - Make sure that you are using versions, which available on https://az-nexus.otpbank.hu/repository/nexus-terraform-provider/.
terraform {
  required_providers {
    azurerm = {
      source  = "hashicorp/azurerm"
      version = "4.10.0"
    }
    time = {
      source  = "hashicorp/time"
      version = "0.11.2"
    }
  }
}

```

## Example for Convention

```hcl
module "conventions" {
  //Checkov  
  #checkov:skip=CKV_TF_1: We strictly use single tags for single hash      
  source      = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-base//conventions?ref=v2.0.3"
  cloud       = var.cloud
  environment = var.environment
  project     = var.project
  region      = var.region
  subsidiary  = var.subsidiary
  tags = {
    "OwnerOU"      = "ccoe"
    "OwnerContact" = var.owner //In the blueprint change the OwnerContact tag to the owner of the solution and remove variable "owner" {}.
    "Criticality"  = "Low"
  }
}

```

## Example for CosmosDB Account for MongoDB creation

```hcl
locals {
  resource_name_suffix = "cosmosdbmongotest-01"
}

module "rg01" {
  //Checkov
  #checkov:skip=CKV_TF_1: We strictly use single tags for single hash  
  source               = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-rg?ref=v1.2.0"
  conventions          = module.conventions
  resource_name_suffix = local.resource_name_suffix
}


module "cosmosdb" {

  // Create Cosmos DB Account first
  // source = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-cosmosdb//mongodb?ref=v2.2.0"
  source               = "../.."
  conventions          = module.conventions
  resource_group_name  = module.rg01.rgrp.name
  resource_name_suffix = local.resource_name_suffix
  subnet_id            = data.azurerm_subnet.sn_privateendpoint.id
  wait_after           = 300
  ip_range_filter      = ["*************", "************", "***********", "************", "*************"] //example IP list allows requests from the Azure portal 

  // Optional: Limit the total throughput providisoned on your Azure Cosmos DB Account
  //total_throughput_limit = 1000

  consistency_policy = {
    consistency_level       = "BoundedStaleness" // can be either BoundedStaleness, Eventual, Session, Strong or ConsistentPrefix
    max_interval_in_seconds = 300
    max_staleness_prefix    = 100000
  }

  /* Example for another consistency policy which does not require additional parameters
  consistency_policy = {
    consistency_level  = "Eventual" 
  }
*/

  // Example for a standalone deployment
  failover_locations = [
    {
      location          = "westeurope"
      failover_priority = 0
      zone_redundant    = false
    }
  ]

  /*
  backup = {
    type                = "Periodic"
    interval_in_minutes = 240
    retention_in_hours  = 8
  } 
*/

  //Diagnostic settings
  log_analytics_workspace_id = module.conventions.log_analytics_workspace_id
  //Example with all metric types:
  log_analytics_metrics = ["AllMetrics"]
  //Example with list of log types:
  //log_analytics_diag_logs    = ["MongoRequests"]
  //Example with all log types:
  log_analytics_diag_logs = ["AllLogs"]


}



```


## Resources

The following resources are used by this module:

- [azurerm_cosmosdb_account.cmdb](https://registry.terraform.io/providers/hashicorp/azurerm/latest/docs/resources/cosmosdb_account) (resource)
- [azurerm_role_assignment.cosmosdb_operator_role_assignment](https://registry.terraform.io/providers/hashicorp/azurerm/latest/docs/resources/role_assignment) (resource)
- [time_sleep.logging_wait](https://registry.terraform.io/providers/hashicorp/time/latest/docs/resources/sleep) (resource)
- [time_sleep.pe_wait](https://registry.terraform.io/providers/hashicorp/time/latest/docs/resources/sleep) (resource)

## Required Inputs

The following input variables are required:

### <a name="input_consistency_policy"></a> [consistency\_policy](#input\_consistency\_policy)

Description: (Required) Consistency levels in Azure Cosmos DB

Type:

```hcl
object({
    consistency_level       = string
    max_interval_in_seconds = optional(number)
    max_staleness_prefix    = optional(number)
  })
```

### <a name="input_conventions"></a> [conventions](#input\_conventions)

Description: (Required) terraform-conventions module

Type: `any`

### <a name="input_resource_group_name"></a> [resource\_group\_name](#input\_resource\_group\_name)

Description: (Required) Specifies the name of the resource group.

Type: `string`

### <a name="input_resource_name_suffix"></a> [resource\_name\_suffix](#input\_resource\_name\_suffix)

Description: (Required) Custom resource name suffix

Type: `string`

### <a name="input_subnet_id"></a> [subnet\_id](#input\_subnet\_id)

Description: (Required) Subnet where private endpoint will be created

Type: `string`

## Optional Inputs

The following input variables are optional (have default values):

### <a name="input_alert_NormalizedRUConsumption_threshold"></a> [alert\_NormalizedRUConsumption\_threshold](#input\_alert\_NormalizedRUConsumption\_threshold)

Description: (Optional) Threshold for Normalized RU Consumption alert rule.

Type: `number`

Default: `90`

### <a name="input_alert_ServiceAvailability_threshold"></a> [alert\_ServiceAvailability\_threshold](#input\_alert\_ServiceAvailability\_threshold)

Description: (Optional) Threshold for Service Availability alert rule.

Type: `number`

Default: `99`

### <a name="input_backup"></a> [backup](#input\_backup)

Description: (Optional) Specifies the backup setting for different types, intervals and retention time in hours that each backup is retained.  Default retention is 7 days on dev, tst and 30 days on ppr, prd.  
  type - (Required) The type of the backup. Possible values are Continuous and Periodic. Migration of Periodic to Continuous is one-way, changing Continuous to Periodic forces a new resource to be created.  
  interval\_in\_minutes - (Optional) The interval in minutes between two backups. This is configurable only when type is Periodic. Possible values are between 60 and 1440.  
  retention\_in\_hours - (Optional) The time in hours that each backup is retained. This is configurable only when type is Periodic. Possible values are between 8 and 720.  
  storage\_redundancy - (Optional) The storage redundancy is used to indicate the type of backup residency. This is configurable only when type is Periodic. Possible values are Geo, Local and Zone.

Type: `map(string)`

Default:

```json
{
  "interval_in_minutes": 240,
  "retention_in_hours": null,
  "storage_redundancy": "Local",
  "type": "Periodic"
}
```

### <a name="input_builtin_metric_monitoring"></a> [builtin\_metric\_monitoring](#input\_builtin\_metric\_monitoring)

Description: (Optional) Set to false if default alerting rules are not required. Defaults to true

Type: `bool`

Default: `true`

### <a name="input_failover_locations"></a> [failover\_locations](#input\_failover\_locations)

Description: (Optional) The name of the Azure region to host replicated data and their priority. One location is always required.

Type:

```hcl
list(object({
    location          = string
    failover_priority = number
    zone_redundant    = optional(bool)
  }))
```

Default: `null`

### <a name="input_ip_range_filter"></a> [ip\_range\_filter](#input\_ip\_range\_filter)

Description: (Optional) This value specifies the set of IP addresses or IP address ranges in CIDR form to be included as the allowed list of client IPs for a given database account. IP addresses/ranges must be comma separated and must not contain any spaces.

Type: `list(string)`

Default:

```json
[
  ""
]
```

### <a name="input_log_analytics_diag_logs"></a> [log\_analytics\_diag\_logs](#input\_log\_analytics\_diag\_logs)

Description: (Optional) List log types need to be sent to Log Analytics Workspace. Set AllLogs to send all available log types. Check available log types: https://learn.microsoft.com/en-us/azure/azure-monitor/essentials/resource-logs-categories

Type: `list(string)`

Default: `[]`

### <a name="input_log_analytics_metrics"></a> [log\_analytics\_metrics](#input\_log\_analytics\_metrics)

Description: (Optional) List metrics need to be sent to Log Analytics Workspace. Set AllMetrics to send all available metric types.

Type: `list(string)`

Default: `[]`

### <a name="input_log_analytics_workspace_id"></a> [log\_analytics\_workspace\_id](#input\_log\_analytics\_workspace\_id)

Description: (Optional) ID of target Log Analytics Workspace

Type: `string`

Default: `null`

### <a name="input_minimal_tls_version"></a> [minimal\_tls\_version](#input\_minimal\_tls\_version)

Description: (Optional) Specifies the minimal TLS version for the CosmosDB account. Possible values are: Tls, Tls11, and Tls12. Defaults to Tls12.

Type: `string`

Default: `"Tls12"`

### <a name="input_mongo_server_version"></a> [mongo\_server\_version](#input\_mongo\_server\_version)

Description: (Optional) The Server Version of a MongoDB account. Possible values are 4.2, 4.0, 3.6, and 3.2. Defaults to 3.6.

Type: `string`

Default: `null`

### <a name="input_mongodb_tags"></a> [mongodb\_tags](#input\_mongodb\_tags)

Description: (Optional) Additional tags to apply to the Mongo DB account and additional resources created by the module

Type: `map(string)`

Default: `null`

### <a name="input_operator_ids"></a> [operator\_ids](#input\_operator\_ids)

Description: (Optional) The Principal IDs of the  object (User, Group or Service Principal) to assign the Cosmos DB Operator role. This role is always assigned to the running service principal.

Type: `list(string)`

Default: `[]`

### <a name="input_pe_subresource_list"></a> [pe\_subresource\_list](#input\_pe\_subresource\_list)

Description: A list of subresource names which the Private Endpoint is able to connect to.

Type: `list(string)`

Default:

```json
[
  "MongoDB"
]
```

### <a name="input_resource_health_alert_location"></a> [resource\_health\_alert\_location](#input\_resource\_health\_alert\_location)

Description: (Optional) Region where the alert rule will be created. Defaults to West Europe, North Europe or global according to conventions settings.

Type: `string`

Default: `null`

### <a name="input_resource_health_monitoring"></a> [resource\_health\_monitoring](#input\_resource\_health\_monitoring)

Description: (Optional) Set to false if resource health alert rule is not required. Defaults to true.

Type: `bool`

Default: `true`

### <a name="input_total_throughput_limit"></a> [total\_throughput\_limit](#input\_total\_throughput\_limit)

Description: (Optional) The total throughput limit imposed on this Cosmos DB account (RU/s). Default is nolimit: -1

Type: `number`

Default: `-1`

### <a name="input_wait_after"></a> [wait\_after](#input\_wait\_after)

Description: (Optional) Seconds to wait after private endpoint is created.

Type: `number`

Default: `600`

## Outputs

The following outputs are exported:

### <a name="output_cmdb"></a> [cmdb](#output\_cmdb)

Description: Azure Cosmos DB for MongoDB

## Contributing

* If you think you've found a bug in the code or you have a question regarding
  the usage of this module, please reach out to <NAME_EMAIL>
* Contributions to this project are welcome: if you want to add a feature or a
  fix a bug, please do so by opening a Pull Request in this repository.
  In case of feature contribution, we kindly ask you to send a mail to
  discuss it beforehand.
<!-- END_TF_DOCS -->