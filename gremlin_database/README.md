## Name of the module
Azure Cosmos DB Gremlin Database

shortname: -

terraform resource: azurerm_cosmosdb_gremlin_database, azurerm_cosmosdb_gremlin_graph

## Short description of the module
This Terraform module deploys an Azure Cosmos DB Gremlin database and collection.

## Detailed description on Confluence
[Azure Cosmos DB ](https://confluence.otpbank.hu/x/7YN6Kw) 

## Release notes – changes in the current and previous versions
[CHANGELOG.md](../CHANGELOG.md)

## Terraform version compatibility
Terraform >= 1.7.4

## Necessary Terraform providers, and compatibility to provider versions
provider registry.terraform.io/hashicorp/azurerm >= 4.2.0

## Resources generated by the module
- Gremlin database and graph

