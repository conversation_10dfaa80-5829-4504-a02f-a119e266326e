variable "conventions" {
  description = "(Required) terraform-conventions module"
}

variable "account_name" {
  type        = string
  description = "(Required) The Cosmos DB Account name."
}

variable "resource_group_name" {
  type        = string
  description = "(Required) The resource group name."
}

variable "gremlin_dbs" {
  type = map(object({
    db_name           = string
    db_throughput     = optional(number)
    db_max_throughput = optional(number)
  }))
  description = "(Required) Cosmos DB Gremlin Databases"
  default     = {}
}

variable "gremlin_graphs" {
  type = map(object({
    graph_name             = string
    db_name                = string
    partition_key_path     = string
    partition_key_version  = optional(number)
    graph_throughput       = optional(number)
    graph_max_throughput   = optional(number)
    default_ttl            = optional(number)
    analytical_storage_ttl = optional(number)
    unique_keys            = optional(list(string), [])
    
    conflict_resolution_policy = optional(object({
      mode                          = string
      conflict_resolution_path      = optional(string)
      conflict_resolution_procedure = optional(string)
    }))
    
    index_policy = optional(object({
      indexing_mode      = optional(string)
      automatic          = optional(bool)
      included_paths     = optional(list(string), [])
      excluded_paths     = optional(list(string), [])
      composite_indexes  = optional(list(list(object({
        path  = string
        order = optional(string)
      }))), [])
      spatial_indexes    = optional(list(string), [])
    }))
  }))
  description = "Map of Gremlin Graphs to create"
  default     = {}
}


