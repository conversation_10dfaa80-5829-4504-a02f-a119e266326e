locals {
  resource_name_suffix = "cosmosdbgremlindb-08"
  suffix = "grecmk08"
}

module "rg01" {
  //Checkov
  #checkov:skip=CKV_TF_1: We strictly use single tags for single hash  
  source               = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-rg?ref=v1.2.0"
  conventions          = module.conventions
  resource_name_suffix = local.resource_name_suffix
}

//Create user assigned identity for cmk
resource "azurerm_user_assigned_identity" "cmk_umid" {
  location            = module.conventions.region
  name                = "umid-${local.suffix}"
  resource_group_name = module.rg01.rgrp.name
}

module "gremlin_key" {
  #checkov:skip=CKV_TF_1:Not relevant in our environment
  source          = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-key-vault//key-vault-key?ref=v1.4.0"
  conventions      = module.conventions
  key_name         = "postgrekey-${local.suffix}"
  key_vault_id     = data.azurerm_key_vault.kv.id
  key_type         = "RSA"
  key_size         = 4096
  key_opts         = ["decrypt","encrypt","sign","unwrapKey","verify","wrapKey"]
  expiration_date = "2025-12-01T19:00:00Z"
}

resource "azurerm_role_assignment" "identity_to_keyvault" {
  scope                = data.azurerm_key_vault.kv.id
  role_definition_name = "Key Vault Crypto Service Encryption User"
  principal_id         = azurerm_user_assigned_identity.cmk_umid.principal_id
}

module "cosmosdb" {

  // Create Cosmos DB Account first

  // source = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-cosmosdb//gremlin?ref=v2.2.0"
  source               = "../../../gremlin"
  conventions          = module.conventions
  resource_group_name  = module.rg01.rgrp.name
  resource_name_suffix = local.resource_name_suffix
  subnet_id            = data.azurerm_subnet.sn_privateendpoint.id
  wait_after           = 120
  ip_range_filter      = ["*************", "************", "***********", "************", "*************"] //example IP list allows requests from the Azure portal 

  // Optional: Limit the total throughput providisoned on your Azure Cosmos DB Account
  // total_throughput_limit = 1000

  consistency_policy = {
    consistency_level       = "BoundedStaleness" ## can be either BoundedStaleness, Eventual, Session, Strong or ConsistentPrefix
    max_interval_in_seconds = 300
    max_staleness_prefix    = 100000
  }

  // Example for a standalone deployment
  failover_locations = [
    {
      location          = "westeurope"
      failover_priority = 0
      zone_redundant    = false
    }
  ]

  backup = {
    type                = "Periodic"
    interval_in_minutes = 240
    retention_in_hours  = 8
  }

  identity_ids       = [azurerm_user_assigned_identity.cmk_umid.id]
  key_vault_key_id   = module.gremlin_key.azurerm_key_vault_key.versionless_id

  //Diagnostic settings
  log_analytics_workspace_id = module.conventions.log_analytics_workspace_id
  //Example with all metric types:
  log_analytics_metrics = ["AllMetrics"]
  //Example with list of log types:
  //log_analytics_diag_logs    = ["MongoRequests"]
  //Example with all log types:
  log_analytics_diag_logs = ["AllLogs"]

  depends_on = [ azurerm_role_assignment.identity_to_keyvault ]
}

module "cosmosdb_gremlin" {
  source = "../.."

  conventions         = module.conventions
  resource_group_name = module.rg01.rgrp.name
  account_name        = module.cosmosdb.cgre.name

  gremlin_dbs = {
    mydb = {
      db_name           = "gremlin-db-01"
      db_throughput     = 400
      db_max_throughput = null
    }
  }

  gremlin_graphs = {
    mygraph = {
      graph_name             = "graph-01"
      db_name                = "gremlin-db-01"
      partition_key_path     = "/pk"
      partition_key_version  = 2
      graph_throughput       = 400
      graph_max_throughput   = null
      default_ttl            = -1
      analytical_storage_ttl = null
      unique_keys            = ["/email"]

      conflict_resolution_policy = {
        mode                     = "LastWriterWins"
        conflict_resolution_path = "/lastModified"
      }

      index_policy = {
        indexing_mode  = "consistent"
        automatic      = true

        # included_paths és excluded_paths list of string
        included_paths = ["/*"]
        excluded_paths = ["/_etag/?"]

        # composite_indexes maradhat nested object
        composite_indexes = [
          [
            { path = "/name", order = "ascending" },
            { path = "/age", order = "descending" }
          ]
        ]

        # spatial_indexes list of string
        spatial_indexes = ["/location/?"]
      }
    }
  }
}
