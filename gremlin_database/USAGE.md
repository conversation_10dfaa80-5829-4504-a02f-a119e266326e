<!-- BEGIN_TF_DOCS -->
## Name of the module
Azure Cosmos DB Gremlin Database

shortname: -

terraform resource: azurerm\_cosmosdb\_gremlin\_database, azurerm\_cosmosdb\_gremlin\_graph

## Short description of the module
This Terraform module deploys an Azure Cosmos DB Gremlin database and collection.

## Detailed description on Confluence
[Azure Cosmos DB ](https://confluence.otpbank.hu/x/7YN6Kw)

## Release notes – changes in the current and previous versions
[CHANGELOG.md](../CHANGELOG.md)

## Terraform version compatibility
Terraform >= 1.7.4

## Necessary Terraform providers, and compatibility to provider versions
provider registry.terraform.io/hashicorp/azurerm >= 4.2.0

## Resources generated by the module
- Gremlin database and graph

## Requirements

The following requirements are needed by this module:

- <a name="requirement_azurerm"></a> [azurerm](#requirement\_azurerm) (>= 4.2.0)

## Providers

The following providers are used by this module:

- <a name="provider_azurerm"></a> [azurerm](#provider\_azurerm) (>= 4.2.0)


## Example for Provider configuration

```hcl
provider "azurerm" {
  features {
    resource_group {
      prevent_deletion_if_contains_resources = false
    }
  }
  storage_use_azuread = true
}

provider "time" {
}

#required_providers - Make sure that you are using versions, which available on https://az-nexus.otpbank.hu/repository/nexus-terraform-provider/.
terraform {
  required_providers {
    azurerm = {
      source  = "hashicorp/azurerm"
      version = "4.42.0"
    }
    time = {
      source  = "hashicorp/time"
      version = "0.11.2"
    }
  }
}

```

## Example for Convention

```hcl
module "conventions" {
  //Checkov  
  #checkov:skip=CKV_TF_1: We strictly use single tags for single hash      
  source      = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-base//conventions?ref=v2.0.3"
  cloud       = var.cloud
  environment = var.environment
  project     = var.project
  region      = var.region
  subsidiary  = var.subsidiary
  tags = {
    "OwnerOU"      = "ccoe"
    "OwnerContact" = var.owner //In the blueprint change the OwnerContact tag to the owner of the solution and remove variable "owner" {}.
    "Criticality"  = "Low"
  }
}

```

## Example for MongoDB Database creation

```hcl
locals {
  resource_name_suffix = "cosmosdbgremlindb-08"
  suffix = "grecmk08"
}

module "rg01" {
  //Checkov
  #checkov:skip=CKV_TF_1: We strictly use single tags for single hash  
  source               = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-rg?ref=v1.2.0"
  conventions          = module.conventions
  resource_name_suffix = local.resource_name_suffix
}

//Create user assigned identity for cmk
resource "azurerm_user_assigned_identity" "cmk_umid" {
  location            = module.conventions.region
  name                = "umid-${local.suffix}"
  resource_group_name = module.rg01.rgrp.name
}

module "gremlin_key" {
  #checkov:skip=CKV_TF_1:Not relevant in our environment
  source          = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-key-vault//key-vault-key?ref=v1.4.0"
  conventions      = module.conventions
  key_name         = "postgrekey-${local.suffix}"
  key_vault_id     = data.azurerm_key_vault.kv.id
  key_type         = "RSA"
  key_size         = 4096
  key_opts         = ["decrypt","encrypt","sign","unwrapKey","verify","wrapKey"]
  expiration_date = "2025-12-01T19:00:00Z"
}

resource "azurerm_role_assignment" "identity_to_keyvault" {
  scope                = data.azurerm_key_vault.kv.id
  role_definition_name = "Key Vault Crypto Service Encryption User"
  principal_id         = azurerm_user_assigned_identity.cmk_umid.principal_id
}

module "cosmosdb" {

  // Create Cosmos DB Account first

  // source = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-cosmosdb//gremlin?ref=v2.2.0"
  source               = "../../../gremlin"
  conventions          = module.conventions
  resource_group_name  = module.rg01.rgrp.name
  resource_name_suffix = local.resource_name_suffix
  subnet_id            = data.azurerm_subnet.sn_privateendpoint.id
  wait_after           = 120
  ip_range_filter      = ["*************", "************", "***********", "************", "*************"] //example IP list allows requests from the Azure portal 

  // Optional: Limit the total throughput providisoned on your Azure Cosmos DB Account
  // total_throughput_limit = 1000

  consistency_policy = {
    consistency_level       = "BoundedStaleness" ## can be either BoundedStaleness, Eventual, Session, Strong or ConsistentPrefix
    max_interval_in_seconds = 300
    max_staleness_prefix    = 100000
  }

  // Example for a standalone deployment
  failover_locations = [
    {
      location          = "westeurope"
      failover_priority = 0
      zone_redundant    = false
    }
  ]

  backup = {
    type                = "Periodic"
    interval_in_minutes = 240
    retention_in_hours  = 8
  }

  identity_ids       = [azurerm_user_assigned_identity.cmk_umid.id]
  key_vault_key_id   = module.gremlin_key.azurerm_key_vault_key.versionless_id

  //Diagnostic settings
  log_analytics_workspace_id = module.conventions.log_analytics_workspace_id
  //Example with all metric types:
  log_analytics_metrics = ["AllMetrics"]
  //Example with list of log types:
  //log_analytics_diag_logs    = ["MongoRequests"]
  //Example with all log types:
  log_analytics_diag_logs = ["AllLogs"]

  depends_on = [ azurerm_role_assignment.identity_to_keyvault ]
}

module "cosmosdb_gremlin" {
  source = "../.."

  conventions         = module.conventions
  resource_group_name = module.rg01.rgrp.name
  account_name        = module.cosmosdb.cgre.name

  gremlin_dbs = {
    mydb = {
      db_name           = "gremlin-db-01"
      db_throughput     = 400
      db_max_throughput = null
    }
  }

  gremlin_graphs = {
    mygraph = {
      graph_name             = "graph-01"
      db_name                = "gremlin-db-01"
      partition_key_path     = "/pk"
      partition_key_version  = 2
      graph_throughput       = 400
      graph_max_throughput   = null
      default_ttl            = -1
      analytical_storage_ttl = null
      unique_keys            = ["/email"]

      conflict_resolution_policy = {
        mode                     = "LastWriterWins"
        conflict_resolution_path = "/lastModified"
      }

      index_policy = {
        indexing_mode  = "consistent"
        automatic      = true

        # included_paths és excluded_paths list of string
        included_paths = ["/*"]
        excluded_paths = ["/_etag/?"]

        # composite_indexes maradhat nested object
        composite_indexes = [
          [
            { path = "/name", order = "ascending" },
            { path = "/age", order = "descending" }
          ]
        ]

        # spatial_indexes list of string
        spatial_indexes = ["/location/?"]
      }
    }
  }
}

```


## Resources

The following resources are used by this module:

- [azurerm_cosmosdb_gremlin_database.main](https://registry.terraform.io/providers/hashicorp/azurerm/latest/docs/resources/cosmosdb_gremlin_database) (resource)
- [azurerm_cosmosdb_gremlin_graph.graph](https://registry.terraform.io/providers/hashicorp/azurerm/latest/docs/resources/cosmosdb_gremlin_graph) (resource)

## Required Inputs

The following input variables are required:

### <a name="input_account_name"></a> [account\_name](#input\_account\_name)

Description: (Required) The Cosmos DB Account name.

Type: `string`

### <a name="input_conventions"></a> [conventions](#input\_conventions)

Description: (Required) terraform-conventions module

Type: `any`

### <a name="input_resource_group_name"></a> [resource\_group\_name](#input\_resource\_group\_name)

Description: (Required) The resource group name.

Type: `string`

## Optional Inputs

The following input variables are optional (have default values):

### <a name="input_gremlin_dbs"></a> [gremlin\_dbs](#input\_gremlin\_dbs)

Description: (Required) Cosmos DB Gremlin Databases

Type:

```hcl
map(object({
    db_name           = string
    db_throughput     = optional(number)
    db_max_throughput = optional(number)
  }))
```

Default: `{}`

### <a name="input_gremlin_graphs"></a> [gremlin\_graphs](#input\_gremlin\_graphs)

Description: Map of Gremlin Graphs to create

Type:

```hcl
map(object({
    graph_name             = string
    db_name                = string
    partition_key_path     = string
    partition_key_version  = optional(number)
    graph_throughput       = optional(number)
    graph_max_throughput   = optional(number)
    default_ttl            = optional(number)
    analytical_storage_ttl = optional(number)
    unique_keys            = optional(list(string), [])
    
    conflict_resolution_policy = optional(object({
      mode                          = string
      conflict_resolution_path      = optional(string)
      conflict_resolution_procedure = optional(string)
    }))
    
    index_policy = optional(object({
      indexing_mode      = optional(string)
      automatic          = optional(bool)
      included_paths     = optional(list(string), [])
      excluded_paths     = optional(list(string), [])
      composite_indexes  = optional(list(list(object({
        path  = string
        order = optional(string)
      }))), [])
      spatial_indexes    = optional(list(string), [])
    }))
  }))
```

Default: `{}`

## Outputs

The following outputs are exported:

### <a name="output_cgre"></a> [cgre](#output\_cgre)

Description: Gremlin database

## Contributing

* If you think you've found a bug in the code or you have a question regarding
  the usage of this module, please reach out to <NAME_EMAIL>
* Contributions to this project are welcome: if you want to add a feature or a
  fix a bug, please do so by opening a Pull Request in this repository.
  In case of feature contribution, we kindly ask you to send a mail to
  discuss it beforehand.
<!-- END_TF_DOCS -->