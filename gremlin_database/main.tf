// CosmosDB Gremlin Database API
//------------------------------
resource "azurerm_cosmosdb_gremlin_database" "main" {
  for_each            = var.gremlin_dbs
  name                = each.value.db_name
  resource_group_name = var.resource_group_name
  account_name        = var.account_name
  throughput          = each.value.db_max_throughput == null ? each.value.db_throughput : null

  dynamic "autoscale_settings" {
    for_each = each.value.db_max_throughput != null ? [1] : []
    content {
      max_throughput = each.value.db_max_throughput
    }
  }
}

// Gremlin Graphs
//----------------
resource "azurerm_cosmosdb_gremlin_graph" "graph" {
  for_each              = var.gremlin_graphs
  name                  = each.value.graph_name
  resource_group_name   = var.resource_group_name
  account_name          = var.account_name
  database_name         = each.value.db_name
  partition_key_path    = each.value.partition_key_path
  partition_key_version = coalesce(each.value.partition_key_version, 2)
  throughput            = each.value.graph_max_throughput != null ? null : each.value.graph_throughput
  default_ttl           = try(each.value.default_ttl, null)
  analytical_storage_ttl = try(each.value.analytical_storage_ttl, null)

  dynamic "autoscale_settings" {
    for_each = each.value.graph_max_throughput != null ? [1] : []
    content {
      max_throughput = each.value.graph_max_throughput
    }
  }

  dynamic "unique_key" {
    for_each = length(try(each.value.unique_keys, [])) > 0 ? [1] : []
    content {
      paths = each.value.unique_keys
    }
  }

  dynamic "conflict_resolution_policy" {
    for_each = each.value.conflict_resolution_policy != null ? [each.value.conflict_resolution_policy] : []
    content {
      mode                          = conflict_resolution_policy.value.mode
      conflict_resolution_path      = try(conflict_resolution_policy.value.conflict_resolution_path, null)
      conflict_resolution_procedure = try(conflict_resolution_policy.value.conflict_resolution_procedure, null)
    }
  }

  dynamic "index_policy" {
    for_each = each.value.index_policy != null ? [each.value.index_policy] : []
    content {
      indexing_mode = try(index_policy.value.indexing_mode, null)
      automatic     = try(index_policy.value.automatic, null)


      included_paths = try(index_policy.value.included_paths, [])
      excluded_paths = try(index_policy.value.excluded_paths, [])

      dynamic "composite_index" {
        for_each = try(index_policy.value.composite_indexes, [])
        content {
          dynamic "index" {
            for_each = composite_index.value
            content {
              path  = index.value.path
              order = try(index.value.order, null)
            }
          }
        }
      }

      dynamic "spatial_index" {
        for_each = try(index_policy.value.spatial_indexes, [])
        content {
          path = spatial_index.value
        }
      }
    }
  }

  depends_on = [
    azurerm_cosmosdb_gremlin_database.main
  ]
}
